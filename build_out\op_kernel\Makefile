# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_kernel/CMakeFiles/progress.marks
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_gen_ops_config: op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule

.PHONY : ascendc_bin_ascend310b_gen_ops_config

# fast build rule for target.
ascendc_bin_ascend310b_gen_ops_config/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build
.PHONY : ascendc_bin_ascend310b_gen_ops_config/fast

# Convenience name for target.
op_kernel/CMakeFiles/binary.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/binary.dir/rule
.PHONY : op_kernel/CMakeFiles/binary.dir/rule

# Convenience name for target.
binary: op_kernel/CMakeFiles/binary.dir/rule

.PHONY : binary

# fast build rule for target.
binary/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/binary.dir/build.make op_kernel/CMakeFiles/binary.dir/build
.PHONY : binary/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_sigmoid_custom_0: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule

.PHONY : ascendc_bin_ascend310b_sigmoid_custom_0

# fast build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_0/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_0/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b: op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule

.PHONY : ascendc_bin_ascend310b

# fast build rule for target.
ascendc_bin_ascend310b/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build
.PHONY : ascendc_bin_ascend310b/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_sigmoid_custom_copy: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule

.PHONY : ascendc_bin_ascend310b_sigmoid_custom_copy

# fast build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_copy/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_copy/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b: op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule

.PHONY : ascendc_bin_ascend910b

# fast build rule for target.
ascendc_bin_ascend910b/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build
.PHONY : ascendc_bin_ascend910b/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_sigmoid_custom_0: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule

.PHONY : ascendc_bin_ascend910b_sigmoid_custom_0

# fast build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_0/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_0/fast

# Convenience name for target.
op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule

# Convenience name for target.
ops_info_gen_ascend310b: op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule

.PHONY : ops_info_gen_ascend310b

# fast build rule for target.
ops_info_gen_ascend310b/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build
.PHONY : ops_info_gen_ascend310b/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule

# Convenience name for target.
ascendc_impl_gen: op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule

.PHONY : ascendc_impl_gen

# fast build rule for target.
ascendc_impl_gen/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_impl_gen.dir/build.make op_kernel/CMakeFiles/ascendc_impl_gen.dir/build
.PHONY : ascendc_impl_gen/fast

# Convenience name for target.
op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule

# Convenience name for target.
ops_info_gen_ascend910b: op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule

.PHONY : ops_info_gen_ascend910b

# fast build rule for target.
ops_info_gen_ascend910b/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build
.PHONY : ops_info_gen_ascend910b/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_gen_ops_config: op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule

.PHONY : ascendc_bin_ascend910b_gen_ops_config

# fast build rule for target.
ascendc_bin_ascend910b_gen_ops_config/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build
.PHONY : ascendc_bin_ascend910b_gen_ops_config/fast

# Convenience name for target.
op_kernel/CMakeFiles/npu_supported_ops.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/npu_supported_ops.dir/rule
.PHONY : op_kernel/CMakeFiles/npu_supported_ops.dir/rule

# Convenience name for target.
npu_supported_ops: op_kernel/CMakeFiles/npu_supported_ops.dir/rule

.PHONY : npu_supported_ops

# fast build rule for target.
npu_supported_ops/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/npu_supported_ops.dir/build.make op_kernel/CMakeFiles/npu_supported_ops.dir/build
.PHONY : npu_supported_ops/fast

# Convenience name for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_sigmoid_custom_copy: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule

.PHONY : ascendc_bin_ascend910b_sigmoid_custom_copy

# fast build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_copy/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_copy/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/local"
	@echo "... install"
	@echo "... ascendc_bin_ascend310b_gen_ops_config"
	@echo "... binary"
	@echo "... ascendc_bin_ascend310b_sigmoid_custom_0"
	@echo "... ascendc_bin_ascend310b"
	@echo "... edit_cache"
	@echo "... ascendc_bin_ascend310b_sigmoid_custom_copy"
	@echo "... ascendc_bin_ascend910b"
	@echo "... install/strip"
	@echo "... ascendc_bin_ascend910b_sigmoid_custom_0"
	@echo "... ops_info_gen_ascend310b"
	@echo "... list_install_components"
	@echo "... ascendc_impl_gen"
	@echo "... rebuild_cache"
	@echo "... ops_info_gen_ascend910b"
	@echo "... ascendc_bin_ascend910b_gen_ops_config"
	@echo "... npu_supported_ops"
	@echo "... package"
	@echo "... ascendc_bin_ascend910b_sigmoid_custom_copy"
	@echo "... package_source"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

