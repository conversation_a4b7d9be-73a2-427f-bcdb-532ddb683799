# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

op_host/CMakeFiles/cust_optiling.dir/sigmoid_custom.cpp.o
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/base_type.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/compute_node_info.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/context_extend.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/expand_dims_type.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_datatype_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_range_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_run_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/op_execute_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/range.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor_data.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_data.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_parse_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_error_codes.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_types.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_error_codes.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/ge/ge_allocator.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/compiler_def.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/gnode.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_factory.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_reg.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_factory.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_registry.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_impl_registry.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/register/tilingdata_base.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/securec.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/securectype.h
 /home/<USER>/Ascend/ascend-toolkit/latest/include/utils/extern_math_util.h
 /home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/sigmoid_custom.cpp
 /home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/sigmoid_custom_tiling.h
