# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/modify_vendor.dir/all
all: CMakeFiles/gen_version_info.dir/all
all: framework/all
all: op_host/all
all: op_kernel/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall: framework/preinstall
preinstall: op_host/preinstall
preinstall: op_kernel/preinstall

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/modify_vendor.dir/clean
clean: CMakeFiles/gen_version_info.dir/clean
clean: framework/clean
clean: op_host/clean
clean: op_kernel/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory framework

# Recursive "all" directory target.
framework/all: framework/tf_plugin/all

.PHONY : framework/all

# Recursive "preinstall" directory target.
framework/preinstall: framework/tf_plugin/preinstall

.PHONY : framework/preinstall

# Recursive "clean" directory target.
framework/clean: framework/tf_plugin/clean

.PHONY : framework/clean

#=============================================================================
# Directory level rules for directory framework/tf_plugin

# Recursive "all" directory target.
framework/tf_plugin/all: framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/all

.PHONY : framework/tf_plugin/all

# Recursive "preinstall" directory target.
framework/tf_plugin/preinstall:

.PHONY : framework/tf_plugin/preinstall

# Recursive "clean" directory target.
framework/tf_plugin/clean: framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/clean

.PHONY : framework/tf_plugin/clean

#=============================================================================
# Directory level rules for directory op_host

# Recursive "all" directory target.
op_host/all: op_host/CMakeFiles/cust_optiling.dir/all
op_host/all: op_host/CMakeFiles/cust_op_proto.dir/all
op_host/all: op_host/CMakeFiles/cust_opapi.dir/all
op_host/all: op_host/CMakeFiles/optiling_compat.dir/all

.PHONY : op_host/all

# Recursive "preinstall" directory target.
op_host/preinstall:

.PHONY : op_host/preinstall

# Recursive "clean" directory target.
op_host/clean: op_host/CMakeFiles/cust_optiling.dir/clean
op_host/clean: op_host/CMakeFiles/cust_op_proto.dir/clean
op_host/clean: op_host/CMakeFiles/cust_opapi.dir/clean
op_host/clean: op_host/CMakeFiles/optiling_compat.dir/clean

.PHONY : op_host/clean

#=============================================================================
# Directory level rules for directory op_kernel

# Recursive "all" directory target.
op_kernel/all: op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/all
op_kernel/all: op_kernel/CMakeFiles/ascendc_impl_gen.dir/all
op_kernel/all: op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/all
op_kernel/all: op_kernel/CMakeFiles/npu_supported_ops.dir/all

.PHONY : op_kernel/all

# Recursive "preinstall" directory target.
op_kernel/preinstall:

.PHONY : op_kernel/preinstall

# Recursive "clean" directory target.
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/binary.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_impl_gen.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/npu_supported_ops.dir/clean
op_kernel/clean: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/clean

.PHONY : op_kernel/clean

#=============================================================================
# Target rules for target CMakeFiles/modify_vendor.dir

# All Build rule for target.
CMakeFiles/modify_vendor.dir/all:
	$(MAKE) -f CMakeFiles/modify_vendor.dir/build.make CMakeFiles/modify_vendor.dir/depend
	$(MAKE) -f CMakeFiles/modify_vendor.dir/build.make CMakeFiles/modify_vendor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=11 "Built target modify_vendor"
.PHONY : CMakeFiles/modify_vendor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/modify_vendor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/modify_vendor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : CMakeFiles/modify_vendor.dir/rule

# Convenience name for target.
modify_vendor: CMakeFiles/modify_vendor.dir/rule

.PHONY : modify_vendor

# clean rule for target.
CMakeFiles/modify_vendor.dir/clean:
	$(MAKE) -f CMakeFiles/modify_vendor.dir/build.make CMakeFiles/modify_vendor.dir/clean
.PHONY : CMakeFiles/modify_vendor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/gen_version_info.dir

# All Build rule for target.
CMakeFiles/gen_version_info.dir/all:
	$(MAKE) -f CMakeFiles/gen_version_info.dir/build.make CMakeFiles/gen_version_info.dir/depend
	$(MAKE) -f CMakeFiles/gen_version_info.dir/build.make CMakeFiles/gen_version_info.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target gen_version_info"
.PHONY : CMakeFiles/gen_version_info.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/gen_version_info.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/gen_version_info.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : CMakeFiles/gen_version_info.dir/rule

# Convenience name for target.
gen_version_info: CMakeFiles/gen_version_info.dir/rule

.PHONY : gen_version_info

# clean rule for target.
CMakeFiles/gen_version_info.dir/clean:
	$(MAKE) -f CMakeFiles/gen_version_info.dir/build.make CMakeFiles/gen_version_info.dir/clean
.PHONY : CMakeFiles/gen_version_info.dir/clean

#=============================================================================
# Target rules for target framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir

# All Build rule for target.
framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/all:
	$(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/depend
	$(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=9,10 "Built target cust_tf_parsers"
.PHONY : framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/all

# Build rule for subdir invocation for target.
framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule

# Convenience name for target.
cust_tf_parsers: framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule

.PHONY : cust_tf_parsers

# clean rule for target.
framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/clean:
	$(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/clean
.PHONY : framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/clean

#=============================================================================
# Target rules for target op_host/CMakeFiles/cust_optiling.dir

# All Build rule for target.
op_host/CMakeFiles/cust_optiling.dir/all:
	$(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/depend
	$(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=7,8 "Built target cust_optiling"
.PHONY : op_host/CMakeFiles/cust_optiling.dir/all

# Build rule for subdir invocation for target.
op_host/CMakeFiles/cust_optiling.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_optiling.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_host/CMakeFiles/cust_optiling.dir/rule

# Convenience name for target.
cust_optiling: op_host/CMakeFiles/cust_optiling.dir/rule

.PHONY : cust_optiling

# clean rule for target.
op_host/CMakeFiles/cust_optiling.dir/clean:
	$(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/clean
.PHONY : op_host/CMakeFiles/cust_optiling.dir/clean

#=============================================================================
# Target rules for target op_host/CMakeFiles/cust_op_proto.dir

# All Build rule for target.
op_host/CMakeFiles/cust_op_proto.dir/all:
	$(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/depend
	$(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=2,3,4 "Built target cust_op_proto"
.PHONY : op_host/CMakeFiles/cust_op_proto.dir/all

# Build rule for subdir invocation for target.
op_host/CMakeFiles/cust_op_proto.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_op_proto.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_host/CMakeFiles/cust_op_proto.dir/rule

# Convenience name for target.
cust_op_proto: op_host/CMakeFiles/cust_op_proto.dir/rule

.PHONY : cust_op_proto

# clean rule for target.
op_host/CMakeFiles/cust_op_proto.dir/clean:
	$(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/clean
.PHONY : op_host/CMakeFiles/cust_op_proto.dir/clean

#=============================================================================
# Target rules for target op_host/CMakeFiles/cust_opapi.dir

# All Build rule for target.
op_host/CMakeFiles/cust_opapi.dir/all:
	$(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/depend
	$(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=5,6 "Built target cust_opapi"
.PHONY : op_host/CMakeFiles/cust_opapi.dir/all

# Build rule for subdir invocation for target.
op_host/CMakeFiles/cust_opapi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_opapi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_host/CMakeFiles/cust_opapi.dir/rule

# Convenience name for target.
cust_opapi: op_host/CMakeFiles/cust_opapi.dir/rule

.PHONY : cust_opapi

# clean rule for target.
op_host/CMakeFiles/cust_opapi.dir/clean:
	$(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/clean
.PHONY : op_host/CMakeFiles/cust_opapi.dir/clean

#=============================================================================
# Target rules for target op_host/CMakeFiles/optiling_compat.dir

# All Build rule for target.
op_host/CMakeFiles/optiling_compat.dir/all: op_host/CMakeFiles/cust_optiling.dir/all
	$(MAKE) -f op_host/CMakeFiles/optiling_compat.dir/build.make op_host/CMakeFiles/optiling_compat.dir/depend
	$(MAKE) -f op_host/CMakeFiles/optiling_compat.dir/build.make op_host/CMakeFiles/optiling_compat.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target optiling_compat"
.PHONY : op_host/CMakeFiles/optiling_compat.dir/all

# Build rule for subdir invocation for target.
op_host/CMakeFiles/optiling_compat.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/optiling_compat.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_host/CMakeFiles/optiling_compat.dir/rule

# Convenience name for target.
optiling_compat: op_host/CMakeFiles/optiling_compat.dir/rule

.PHONY : optiling_compat

# clean rule for target.
op_host/CMakeFiles/optiling_compat.dir/clean:
	$(MAKE) -f op_host/CMakeFiles/optiling_compat.dir/build.make op_host/CMakeFiles/optiling_compat.dir/clean
.PHONY : op_host/CMakeFiles/optiling_compat.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend310b_gen_ops_config"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_gen_ops_config: op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/rule

.PHONY : ascendc_bin_ascend310b_gen_ops_config

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/binary.dir

# All Build rule for target.
op_kernel/CMakeFiles/binary.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/all
op_kernel/CMakeFiles/binary.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/binary.dir/build.make op_kernel/CMakeFiles/binary.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/binary.dir/build.make op_kernel/CMakeFiles/binary.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target binary"
.PHONY : op_kernel/CMakeFiles/binary.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/binary.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/binary.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/binary.dir/rule

# Convenience name for target.
binary: op_kernel/CMakeFiles/binary.dir/rule

.PHONY : binary

# clean rule for target.
op_kernel/CMakeFiles/binary.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/binary.dir/build.make op_kernel/CMakeFiles/binary.dir/clean
.PHONY : op_kernel/CMakeFiles/binary.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/all
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend310b_sigmoid_custom_0"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_sigmoid_custom_0: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/rule

.PHONY : ascendc_bin_ascend310b_sigmoid_custom_0

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/all: op_kernel/CMakeFiles/ascendc_impl_gen.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend310b"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b: op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/rule

.PHONY : ascendc_bin_ascend310b

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend310b_sigmoid_custom_copy"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule

# Convenience name for target.
ascendc_bin_ascend310b_sigmoid_custom_copy: op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/rule

.PHONY : ascendc_bin_ascend310b_sigmoid_custom_copy

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/all: op_kernel/CMakeFiles/ascendc_impl_gen.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend910b"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b: op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/rule

.PHONY : ascendc_bin_ascend910b

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/all
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend910b_sigmoid_custom_0"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_sigmoid_custom_0: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/rule

.PHONY : ascendc_bin_ascend910b_sigmoid_custom_0

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir

# All Build rule for target.
op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=13 "Built target ops_info_gen_ascend310b"
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule

# Convenience name for target.
ops_info_gen_ascend310b: op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/rule

.PHONY : ops_info_gen_ascend310b

# clean rule for target.
op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/clean
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_impl_gen.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_impl_gen.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_impl_gen.dir/build.make op_kernel/CMakeFiles/ascendc_impl_gen.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_impl_gen.dir/build.make op_kernel/CMakeFiles/ascendc_impl_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=1 "Built target ascendc_impl_gen"
.PHONY : op_kernel/CMakeFiles/ascendc_impl_gen.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_impl_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule

# Convenience name for target.
ascendc_impl_gen: op_kernel/CMakeFiles/ascendc_impl_gen.dir/rule

.PHONY : ascendc_impl_gen

# clean rule for target.
op_kernel/CMakeFiles/ascendc_impl_gen.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_impl_gen.dir/build.make op_kernel/CMakeFiles/ascendc_impl_gen.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_impl_gen.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir

# All Build rule for target.
op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=14 "Built target ops_info_gen_ascend910b"
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule

# Convenience name for target.
ops_info_gen_ascend910b: op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/rule

.PHONY : ops_info_gen_ascend910b

# clean rule for target.
op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/clean
.PHONY : op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/all: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/all
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend910b_gen_ops_config"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_gen_ops_config: op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/rule

.PHONY : ascendc_bin_ascend910b_gen_ops_config

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/npu_supported_ops.dir

# All Build rule for target.
op_kernel/CMakeFiles/npu_supported_ops.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/npu_supported_ops.dir/build.make op_kernel/CMakeFiles/npu_supported_ops.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/npu_supported_ops.dir/build.make op_kernel/CMakeFiles/npu_supported_ops.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num=12 "Built target npu_supported_ops"
.PHONY : op_kernel/CMakeFiles/npu_supported_ops.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/npu_supported_ops.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/npu_supported_ops.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/npu_supported_ops.dir/rule

# Convenience name for target.
npu_supported_ops: op_kernel/CMakeFiles/npu_supported_ops.dir/rule

.PHONY : npu_supported_ops

# clean rule for target.
op_kernel/CMakeFiles/npu_supported_ops.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/npu_supported_ops.dir/build.make op_kernel/CMakeFiles/npu_supported_ops.dir/clean
.PHONY : op_kernel/CMakeFiles/npu_supported_ops.dir/clean

#=============================================================================
# Target rules for target op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir

# All Build rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/all:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/depend
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles --progress-num= "Built target ascendc_bin_ascend910b_sigmoid_custom_copy"
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/all

# Build rule for subdir invocation for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule

# Convenience name for target.
ascendc_bin_ascend910b_sigmoid_custom_copy: op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/rule

.PHONY : ascendc_bin_ascend910b_sigmoid_custom_copy

# clean rule for target.
op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/clean:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/clean
.PHONY : op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

