#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Ascend/ascend-toolkit/latest/include/acl/acl_base.h
stdint.h
-
stddef.h
-
error_codes/rt_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/acl/error_codes/rt_error_codes.h
error_codes/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/acl/error_codes/ge_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/acl/error_codes/ge_error_codes.h
stddef.h
-
stdint.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/acl/error_codes/rt_error_codes.h
stddef.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/aclnn/acl_meta.h
cstdint
-
cstdlib
-
acl/acl_base.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
atomic
-
memory
-
vector
-

/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/autogen/aclnn_sigmoid_custom.cpp
string.h
-
graph/types.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/autogen/graph/types.h
aclnn_sigmoid_custom.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/autogen/aclnn_sigmoid_custom.h

/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/autogen/aclnn_sigmoid_custom.h
aclnn/acl_meta.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/autogen/aclnn/acl_meta.h

