/usr/bin/c++ -fPIC  -s -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared -Wl,-soname,libcust_opsproto_rt2.0.so -o libcust_opsproto_rt2.0.so CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o   -L/home/<USER>/Ascend/ascend-toolkit/latest/lib64  -Wl,-rpath,/home/<USER>/Ascend/ascend-toolkit/latest/lib64: -lexe_graph -lregister -ltiling_api -Wl,--whole-archive -lrt2_registry -Wl,--no-whole-archive 
