.TH "MAKESELF" "1" "2.4.5"
.SH "NAME"
makeself \- An utility to generate self-extractable archives.
.SH "SYNTAX"
.B makeself [\fIoptions\fP] archive_dir file_name label
.B [\fIstartup_script\fP] [\fIargs\fP]
.SH "DESCRIPTION"
This program is a free (GPL) utility designed to create self-extractable
archives from a directory.
.SH "OPTIONS"
The following options are supported.
.TP 15
.B -v, --version
Prints out the makeself version number and exits.
.TP
.B -h, --help
Print out help information.
.TP
.B --tar-quietly
Suppress verbose output from the tar command
.TP
.B --quiet
Do not print any messages other than errors
.TP
.B --gzip
Compress using gzip (default if detected).
.TP
.B --bzip2
Compress using bzip2.
.TP
.B --pbzip2
Compress using pbzip2.
.TP
.B --xz
Compress using xz.
.TP
.B --lzo
Compress using lzop.
.TP
.B --lz4
Compress using lz4.
.TP
.B --compress
Compress using the UNIX 'compress' command.
.TP
.B --nocomp
Do not compress the data.
.TP
.B --complevel lvl
Specify the compression level for gzip,bzip2,pbzui2,xz,lzo or lz4
.TP
.B --notemp
The archive will create archive_dir in the current directory and
uncompress in ./archive_dir.
.TP
.B --copy
Upon extraction, the archive will first copy itself to a temporary directory.
.TP
.B --append
Append more files to an existing makeself archive. The label and startup scripts will then be ignored.
.TP
.B --current
Files will be extracted to the current directory. Both --current and --target dir imply --notemp.
.TP
.B --target dir
Extract directly to a target directory. Directory path can be either absolute or relative.
.TP
.B --header file
Specify location of the header script.
.TP
.B --cleanup file
Specify a cleanup script that executes on interrupt and when finished successfully.
.TP
.B --follow
Follow the symlinks in the archive.
.TP
.B --noprogress
Do not show the progress during the decompression.
.TP
.B --nox11
Disable automatic spawn of an xterm if running in X11.
.TP
.B --nowait
Do not wait for user input after executing embedded program from an xterm.
.TP
.B --nomd5
Do not create a MD5 checksum for the archive.
.TP
.B --nocrc
Do not create a CRC32 checksum for the archive.
.TP
.B --lsm file
LSM file describing the package.
.B --packaging-date date
Use provided string as the packaging date instead of the current date.
.SH "EXAMPLES"
Here is an example, assuming the user has a package image stored in a /home/<USER>/mysoft,
and he wants to generate a self-extracting package named mysoft.sh, which will launch
the "setup" script initially stored in /home/<USER>/mysoft:
.TP
makeself.sh /home/<USER>/mysoft mysoft.sh "Joe's Nice Software Package" ./setup
.TP
Here is also how I created the makeself.run archive which contains the Makeself distribution:
.TP
makeself.sh --notemp makeself makeself.run "Makeself by Stephane Peter" echo "Makeself has extracted itself"
.SH "AUTHORS"
Makeself has been written by Stéphane Peter <<EMAIL>>.
.BR
This man page was originally written by Bartosz Fenski <<EMAIL>> for the
Debian GNU/Linux distribution (but it may be used by others).
