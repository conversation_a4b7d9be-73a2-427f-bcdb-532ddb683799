[{"op": "ScatterReduce", "language": "cpp", "input_desc": [{"name": "self", "param_type": "required", "format": ["ND", "ND"], "type": ["fp32", "fp16"]}, {"name": "index", "param_type": "required", "format": ["ND", "ND"], "type": ["int32", "int32"]}, {"name": "src", "param_type": "required", "format": ["ND", "ND"], "type": ["fp32", "fp16"]}], "output_desc": [{"name": "y", "param_type": "required", "format": ["ND", "ND"], "type": ["fp32", "fp16"]}], "attr": [{"name": "dim", "param_type": "required", "type": "int"}, {"name": "reduce", "param_type": "required", "type": "string"}, {"name": "include_self", "param_type": "optional", "type": "bool", "default_value": "true"}]}]