# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	/usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	/usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named modify_vendor

# Build rule for target.
modify_vendor: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 modify_vendor
.PHONY : modify_vendor

# fast build rule for target.
modify_vendor/fast:
	$(MAKE) -f CMakeFiles/modify_vendor.dir/build.make CMakeFiles/modify_vendor.dir/build
.PHONY : modify_vendor/fast

#=============================================================================
# Target rules for targets named gen_version_info

# Build rule for target.
gen_version_info: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 gen_version_info
.PHONY : gen_version_info

# fast build rule for target.
gen_version_info/fast:
	$(MAKE) -f CMakeFiles/gen_version_info.dir/build.make CMakeFiles/gen_version_info.dir/build
.PHONY : gen_version_info/fast

#=============================================================================
# Target rules for targets named cust_tf_parsers

# Build rule for target.
cust_tf_parsers: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 cust_tf_parsers
.PHONY : cust_tf_parsers

# fast build rule for target.
cust_tf_parsers/fast:
	$(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build
.PHONY : cust_tf_parsers/fast

#=============================================================================
# Target rules for targets named cust_optiling

# Build rule for target.
cust_optiling: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 cust_optiling
.PHONY : cust_optiling

# fast build rule for target.
cust_optiling/fast:
	$(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/build
.PHONY : cust_optiling/fast

#=============================================================================
# Target rules for targets named cust_op_proto

# Build rule for target.
cust_op_proto: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 cust_op_proto
.PHONY : cust_op_proto

# fast build rule for target.
cust_op_proto/fast:
	$(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/build
.PHONY : cust_op_proto/fast

#=============================================================================
# Target rules for targets named cust_opapi

# Build rule for target.
cust_opapi: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 cust_opapi
.PHONY : cust_opapi

# fast build rule for target.
cust_opapi/fast:
	$(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/build
.PHONY : cust_opapi/fast

#=============================================================================
# Target rules for targets named optiling_compat

# Build rule for target.
optiling_compat: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 optiling_compat
.PHONY : optiling_compat

# fast build rule for target.
optiling_compat/fast:
	$(MAKE) -f op_host/CMakeFiles/optiling_compat.dir/build.make op_host/CMakeFiles/optiling_compat.dir/build
.PHONY : optiling_compat/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend310b_gen_ops_config

# Build rule for target.
ascendc_bin_ascend310b_gen_ops_config: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend310b_gen_ops_config
.PHONY : ascendc_bin_ascend310b_gen_ops_config

# fast build rule for target.
ascendc_bin_ascend310b_gen_ops_config/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/build
.PHONY : ascendc_bin_ascend310b_gen_ops_config/fast

#=============================================================================
# Target rules for targets named binary

# Build rule for target.
binary: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 binary
.PHONY : binary

# fast build rule for target.
binary/fast:
	$(MAKE) -f op_kernel/CMakeFiles/binary.dir/build.make op_kernel/CMakeFiles/binary.dir/build
.PHONY : binary/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend310b_sigmoid_custom_0

# Build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_0: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend310b_sigmoid_custom_0
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_0

# fast build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_0/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/build
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_0/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend310b

# Build rule for target.
ascendc_bin_ascend310b: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend310b
.PHONY : ascendc_bin_ascend310b

# fast build rule for target.
ascendc_bin_ascend310b/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/build
.PHONY : ascendc_bin_ascend310b/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend310b_sigmoid_custom_copy

# Build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_copy: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend310b_sigmoid_custom_copy
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_copy

# fast build rule for target.
ascendc_bin_ascend310b_sigmoid_custom_copy/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/build
.PHONY : ascendc_bin_ascend310b_sigmoid_custom_copy/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend910b

# Build rule for target.
ascendc_bin_ascend910b: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend910b
.PHONY : ascendc_bin_ascend910b

# fast build rule for target.
ascendc_bin_ascend910b/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/build
.PHONY : ascendc_bin_ascend910b/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend910b_sigmoid_custom_0

# Build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_0: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend910b_sigmoid_custom_0
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_0

# fast build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_0/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/build
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_0/fast

#=============================================================================
# Target rules for targets named ops_info_gen_ascend310b

# Build rule for target.
ops_info_gen_ascend310b: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ops_info_gen_ascend310b
.PHONY : ops_info_gen_ascend310b

# fast build rule for target.
ops_info_gen_ascend310b/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/build
.PHONY : ops_info_gen_ascend310b/fast

#=============================================================================
# Target rules for targets named ascendc_impl_gen

# Build rule for target.
ascendc_impl_gen: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_impl_gen
.PHONY : ascendc_impl_gen

# fast build rule for target.
ascendc_impl_gen/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_impl_gen.dir/build.make op_kernel/CMakeFiles/ascendc_impl_gen.dir/build
.PHONY : ascendc_impl_gen/fast

#=============================================================================
# Target rules for targets named ops_info_gen_ascend910b

# Build rule for target.
ops_info_gen_ascend910b: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ops_info_gen_ascend910b
.PHONY : ops_info_gen_ascend910b

# fast build rule for target.
ops_info_gen_ascend910b/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build.make op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/build
.PHONY : ops_info_gen_ascend910b/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend910b_gen_ops_config

# Build rule for target.
ascendc_bin_ascend910b_gen_ops_config: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend910b_gen_ops_config
.PHONY : ascendc_bin_ascend910b_gen_ops_config

# fast build rule for target.
ascendc_bin_ascend910b_gen_ops_config/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/build
.PHONY : ascendc_bin_ascend910b_gen_ops_config/fast

#=============================================================================
# Target rules for targets named npu_supported_ops

# Build rule for target.
npu_supported_ops: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 npu_supported_ops
.PHONY : npu_supported_ops

# fast build rule for target.
npu_supported_ops/fast:
	$(MAKE) -f op_kernel/CMakeFiles/npu_supported_ops.dir/build.make op_kernel/CMakeFiles/npu_supported_ops.dir/build
.PHONY : npu_supported_ops/fast

#=============================================================================
# Target rules for targets named ascendc_bin_ascend910b_sigmoid_custom_copy

# Build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_copy: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ascendc_bin_ascend910b_sigmoid_custom_copy
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_copy

# fast build rule for target.
ascendc_bin_ascend910b_sigmoid_custom_copy/fast:
	$(MAKE) -f op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build.make op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/build
.PHONY : ascendc_bin_ascend910b_sigmoid_custom_copy/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... modify_vendor"
	@echo "... edit_cache"
	@echo "... gen_version_info"
	@echo "... package"
	@echo "... package_source"
	@echo "... cust_tf_parsers"
	@echo "... cust_optiling"
	@echo "... cust_op_proto"
	@echo "... cust_opapi"
	@echo "... optiling_compat"
	@echo "... ascendc_bin_ascend310b_gen_ops_config"
	@echo "... binary"
	@echo "... ascendc_bin_ascend310b_sigmoid_custom_0"
	@echo "... ascendc_bin_ascend310b"
	@echo "... ascendc_bin_ascend310b_sigmoid_custom_copy"
	@echo "... ascendc_bin_ascend910b"
	@echo "... ascendc_bin_ascend910b_sigmoid_custom_0"
	@echo "... ops_info_gen_ascend310b"
	@echo "... ascendc_impl_gen"
	@echo "... ops_info_gen_ascend910b"
	@echo "... ascendc_bin_ascend910b_gen_ops_config"
	@echo "... npu_supported_ops"
	@echo "... ascendc_bin_ascend910b_sigmoid_custom_copy"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

