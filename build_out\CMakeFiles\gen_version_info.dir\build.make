# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

# Utility rule file for gen_version_info.

# Include the progress variables for this target.
include CMakeFiles/gen_version_info.dir/progress.make

CMakeFiles/gen_version_info:
	bash /home/<USER>/work/SigmoidCustom/SigmoidCustom/cmake/util/gen_version_info.sh /home/<USER>/Ascend/ascend-toolkit/latest /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

gen_version_info: CMakeFiles/gen_version_info
gen_version_info: CMakeFiles/gen_version_info.dir/build.make

.PHONY : gen_version_info

# Rule to build all files generated by this target.
CMakeFiles/gen_version_info.dir/build: gen_version_info

.PHONY : CMakeFiles/gen_version_info.dir/build

CMakeFiles/gen_version_info.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gen_version_info.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gen_version_info.dir/clean

CMakeFiles/gen_version_info.dir/depend:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/SigmoidCustom/SigmoidCustom /home/<USER>/work/SigmoidCustom/SigmoidCustom /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles/gen_version_info.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/gen_version_info.dir/depend

