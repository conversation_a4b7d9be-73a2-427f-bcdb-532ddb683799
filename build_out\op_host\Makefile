# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host/CMakeFiles/progress.marks
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
op_host/CMakeFiles/cust_optiling.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_optiling.dir/rule
.PHONY : op_host/CMakeFiles/cust_optiling.dir/rule

# Convenience name for target.
cust_optiling: op_host/CMakeFiles/cust_optiling.dir/rule

.PHONY : cust_optiling

# fast build rule for target.
cust_optiling/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/build
.PHONY : cust_optiling/fast

# Convenience name for target.
op_host/CMakeFiles/cust_op_proto.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_op_proto.dir/rule
.PHONY : op_host/CMakeFiles/cust_op_proto.dir/rule

# Convenience name for target.
cust_op_proto: op_host/CMakeFiles/cust_op_proto.dir/rule

.PHONY : cust_op_proto

# fast build rule for target.
cust_op_proto/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/build
.PHONY : cust_op_proto/fast

# Convenience name for target.
op_host/CMakeFiles/cust_opapi.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/cust_opapi.dir/rule
.PHONY : op_host/CMakeFiles/cust_opapi.dir/rule

# Convenience name for target.
cust_opapi: op_host/CMakeFiles/cust_opapi.dir/rule

.PHONY : cust_opapi

# fast build rule for target.
cust_opapi/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/build
.PHONY : cust_opapi/fast

# Convenience name for target.
op_host/CMakeFiles/optiling_compat.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 op_host/CMakeFiles/optiling_compat.dir/rule
.PHONY : op_host/CMakeFiles/optiling_compat.dir/rule

# Convenience name for target.
optiling_compat: op_host/CMakeFiles/optiling_compat.dir/rule

.PHONY : optiling_compat

# fast build rule for target.
optiling_compat/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/optiling_compat.dir/build.make op_host/CMakeFiles/optiling_compat.dir/build
.PHONY : optiling_compat/fast

__/autogen/aclnn_sigmoid_custom.o: __/autogen/aclnn_sigmoid_custom.cpp.o

.PHONY : __/autogen/aclnn_sigmoid_custom.o

# target to build an object file
__/autogen/aclnn_sigmoid_custom.cpp.o:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/__/autogen/aclnn_sigmoid_custom.cpp.o
.PHONY : __/autogen/aclnn_sigmoid_custom.cpp.o

__/autogen/aclnn_sigmoid_custom.i: __/autogen/aclnn_sigmoid_custom.cpp.i

.PHONY : __/autogen/aclnn_sigmoid_custom.i

# target to preprocess a source file
__/autogen/aclnn_sigmoid_custom.cpp.i:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/__/autogen/aclnn_sigmoid_custom.cpp.i
.PHONY : __/autogen/aclnn_sigmoid_custom.cpp.i

__/autogen/aclnn_sigmoid_custom.s: __/autogen/aclnn_sigmoid_custom.cpp.s

.PHONY : __/autogen/aclnn_sigmoid_custom.s

# target to generate assembly for a file
__/autogen/aclnn_sigmoid_custom.cpp.s:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_opapi.dir/build.make op_host/CMakeFiles/cust_opapi.dir/__/autogen/aclnn_sigmoid_custom.cpp.s
.PHONY : __/autogen/aclnn_sigmoid_custom.cpp.s

__/autogen/op_proto.o: __/autogen/op_proto.cc.o

.PHONY : __/autogen/op_proto.o

# target to build an object file
__/autogen/op_proto.cc.o:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o
.PHONY : __/autogen/op_proto.cc.o

__/autogen/op_proto.i: __/autogen/op_proto.cc.i

.PHONY : __/autogen/op_proto.i

# target to preprocess a source file
__/autogen/op_proto.cc.i:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.i
.PHONY : __/autogen/op_proto.cc.i

__/autogen/op_proto.s: __/autogen/op_proto.cc.s

.PHONY : __/autogen/op_proto.s

# target to generate assembly for a file
__/autogen/op_proto.cc.s:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.s
.PHONY : __/autogen/op_proto.cc.s

sigmoid_custom.o: sigmoid_custom.cpp.o

.PHONY : sigmoid_custom.o

# target to build an object file
sigmoid_custom.cpp.o:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/sigmoid_custom.cpp.o
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o
.PHONY : sigmoid_custom.cpp.o

sigmoid_custom.i: sigmoid_custom.cpp.i

.PHONY : sigmoid_custom.i

# target to preprocess a source file
sigmoid_custom.cpp.i:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/sigmoid_custom.cpp.i
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.i
.PHONY : sigmoid_custom.cpp.i

sigmoid_custom.s: sigmoid_custom.cpp.s

.PHONY : sigmoid_custom.s

# target to generate assembly for a file
sigmoid_custom.cpp.s:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_optiling.dir/build.make op_host/CMakeFiles/cust_optiling.dir/sigmoid_custom.cpp.s
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f op_host/CMakeFiles/cust_op_proto.dir/build.make op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.s
.PHONY : sigmoid_custom.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... cust_optiling"
	@echo "... list_install_components"
	@echo "... cust_op_proto"
	@echo "... cust_opapi"
	@echo "... optiling_compat"
	@echo "... package"
	@echo "... package_source"
	@echo "... __/autogen/aclnn_sigmoid_custom.o"
	@echo "... __/autogen/aclnn_sigmoid_custom.i"
	@echo "... __/autogen/aclnn_sigmoid_custom.s"
	@echo "... __/autogen/op_proto.o"
	@echo "... __/autogen/op_proto.i"
	@echo "... __/autogen/op_proto.s"
	@echo "... sigmoid_custom.o"
	@echo "... sigmoid_custom.i"
	@echo "... sigmoid_custom.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

