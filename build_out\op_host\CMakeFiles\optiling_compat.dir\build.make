# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

# Utility rule file for optiling_compat.

# Include the progress variables for this target.
include op_host/CMakeFiles/optiling_compat.dir/progress.make

op_host/CMakeFiles/optiling_compat:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host && ln -sf lib/linux/aarch64/libcust_opmaster_rt2.0.so /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host/liboptiling.so

optiling_compat: op_host/CMakeFiles/optiling_compat
optiling_compat: op_host/CMakeFiles/optiling_compat.dir/build.make

.PHONY : optiling_compat

# Rule to build all files generated by this target.
op_host/CMakeFiles/optiling_compat.dir/build: optiling_compat

.PHONY : op_host/CMakeFiles/optiling_compat.dir/build

op_host/CMakeFiles/optiling_compat.dir/clean:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host && $(CMAKE_COMMAND) -P CMakeFiles/optiling_compat.dir/cmake_clean.cmake
.PHONY : op_host/CMakeFiles/optiling_compat.dir/clean

op_host/CMakeFiles/optiling_compat.dir/depend:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/SigmoidCustom/SigmoidCustom /home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/op_host/CMakeFiles/optiling_compat.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : op_host/CMakeFiles/optiling_compat.dir/depend

