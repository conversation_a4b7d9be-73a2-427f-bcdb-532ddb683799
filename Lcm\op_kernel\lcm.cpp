#include "kernel_operator.h"
using namespace AscendC;

constexpr int32_t BUFFER_NUM = 2;

class Lcmkernel {
private:
    TPipe pipe;
    TQue<QuePosition::VECIN, BUFFER_NUM> inQueueInput;
    TQue<QuePosition::VECIN, BUFFER_NUM> inQueueOther;
    TQue<QuePosition::VECOUT, BUFFER_NUM> outQueueOutput;
    
    // 使用uint8_t基础类型，后续根据数据类型转换
    GlobalTensor<uint8_t> inputGm;    
    GlobalTensor<uint8_t> otherGm;    
    GlobalTensor<uint8_t> outputGm;   

    // 临时计算缓冲区
    TBuf<QuePosition::VECCALC> tmpBuffer1, tmpBuffer2;
    uint32_t blockLength;
    uint32_t tileNum;
    uint32_t tileLength;
    uint32_t dtypeSize; // 数据类型大小（字节）

public:
    __aicore__ inline Lcmkernel() {}
    
    __aicore__ inline void Init(GM_ADDR input, GM_ADDR other, GM_ADDR output, 
                               uint32_t totalLength, uint32_t tileNum, uint32_t output_dtype)
    {
        // 参数校验
        ASSERT(GetBlockNum() != 0 && "block dim can not be zero!");
        this->blockLength = totalLength / GetBlockNum();
        this->tileNum = tileNum;
        ASSERT(tileNum != 0 && "tile num can not be zero!");
        
        // 根据数据类型设置dtypeSize
        switch (output_dtype) {
            case 0: // DT_INT8
                dtypeSize = sizeof(int8_t);
                break;
            case 1: // DT_INT16
                dtypeSize = sizeof(int16_t);
                break;
            case 2: // DT_INT32
                dtypeSize = sizeof(int32_t);
                break;
            case 3: // DT_INT64
                dtypeSize = sizeof(int64_t);
                break;
            default:
                dtypeSize = sizeof(int32_t); // 默认
        }
        
        // 计算每个tile的元素个数（注意：tileLength现在是元素个数，不是字节数）
        this->tileLength = this->blockLength / tileNum / BUFFER_NUM;

        // 设置全局内存缓冲区（使用uint8_t*，按字节偏移）[5,9](@ref)
        inputGm.SetGlobalBuffer(reinterpret_cast<__gm__ uint8_t*>(input) + this->blockLength * dtypeSize * GetBlockIdx(), 
                               this->blockLength * dtypeSize);
        otherGm.SetGlobalBuffer(reinterpret_cast<__gm__ uint8_t*>(other) + this->blockLength * dtypeSize * GetBlockIdx(), 
                               this->blockLength * dtypeSize);
        outputGm.SetGlobalBuffer(reinterpret_cast<__gm__ uint8_t*>(output) + this->blockLength * dtypeSize * GetBlockIdx(), 
                                this->blockLength * dtypeSize);

        // 初始化管道缓冲区（根据数据类型大小分配）[3](@ref)
        pipe.InitBuffer(inQueueInput, BUFFER_NUM, this->tileLength * dtypeSize);
        pipe.InitBuffer(inQueueOther, BUFFER_NUM, this->tileLength * dtypeSize);
        pipe.InitBuffer(outQueueOutput, BUFFER_NUM, this->tileLength * dtypeSize);
        
        // 初始化临时计算缓冲区（足够存储一个tile的数据）
        pipe.InitBuffer(tmpBuffer1, this->tileLength * dtypeSize);
        pipe.InitBuffer(tmpBuffer2, this->tileLength * dtypeSize);
    }
    
    __aicore__ inline void Process()
    {
        int32_t loopCount = this->blockLength / this->tileLength;
        for (int32_t i = 0; i < loopCount; i++) {
            CopyIn(i);
            Compute(i);
            CopyOut(i);
        }
    }

private:
    __aicore__ inline void CopyIn(int32_t progress)
    {
        // 搬入input数据（使用字节偏移）[9](@ref)
        LocalTensor<uint8_t> inputLocal = inQueueInput.AllocTensor<uint8_t>();
        DataCopy(inputLocal, inputGm[progress * this->tileLength * dtypeSize], this->tileLength * dtypeSize);
        inQueueInput.EnQue(inputLocal);
        
        // 搬入other数据
        LocalTensor<uint8_t> otherLocal = inQueueOther.AllocTensor<uint8_t>();
        DataCopy(otherLocal, otherGm[progress * this->tileLength * dtypeSize], this->tileLength * dtypeSize);
        inQueueOther.EnQue(otherLocal);
    }
    
    template <typename T>
    __aicore__ inline void ComputeImpl(LocalTensor<uint8_t>& inputLocal, LocalTensor<uint8_t>& otherLocal, LocalTensor<uint8_t>& outputLocal) {
        // 将uint8_t类型的LocalTensor转换为实际类型的LocalTensor
        LocalTensor<T> inputTyped = inputLocal.ReinterpretCast<T>();
        LocalTensor<T> otherTyped = otherLocal.ReinterpretCast<T>();
        LocalTensor<T> outputTyped = outputLocal.ReinterpretCast<T>();
        
        // 逐元素计算LCM
        for (uint32_t i = 0; i < this->tileLength; ++i) {
            T a = inputTyped.GetValue(i);
            T b = otherTyped.GetValue(i);
            outputTyped.SetValue(i, lcm_device(a, b));
        }
    }
    
    __aicore__ inline void Compute(int32_t progress)
    {
        // 1. 从输入队列取出数据
        LocalTensor<uint8_t> inputLocal = inQueueInput.DeQue<uint8_t>();
        LocalTensor<uint8_t> otherLocal = inQueueOther.DeQue<uint8_t>();
        
        // 2. 分配输出缓冲区
        LocalTensor<uint8_t> outputLocal = outQueueOutput.AllocTensor<uint8_t>();
        
        // 3. 获取Tiling参数（假设已定义）
        GET_TILING_DATA(tiling_data, tiling);
        uint32_t output_dtype = tiling_data.output_dtype;
        
        // 4. 根据数据类型分派计算
        switch (output_dtype) {
            case 0: // int8
                ComputeImpl<int8_t>(inputLocal, otherLocal, outputLocal);
                break;
            case 1: // int16
                ComputeImpl<int16_t>(inputLocal, otherLocal, outputLocal);
                break;
            case 2: // int32
                ComputeImpl<int32_t>(inputLocal, otherLocal, outputLocal);
                break;
            case 3: // int64
                ComputeImpl<int64_t>(inputLocal, otherLocal, outputLocal);
                break;
            default: // 默认int32
                ComputeImpl<int32_t>(inputLocal, otherLocal, outputLocal);
        }
        
        // 5. 结果入队并释放输入
        outQueueOutput.EnQue(outputLocal);
        inQueueInput.FreeTensor(inputLocal);
        inQueueOther.FreeTensor(otherLocal);
    }
    
    __aicore__ inline void CopyOut(int32_t progress)
    {
        // 从队列取出计算结果
        LocalTensor<uint8_t> outputLocal = outQueueOutput.DeQue<uint8_t>();
        // 复制到全局内存（使用字节偏移）[3,11](@ref)
        DataCopy(outputGm[progress * this->tileLength * dtypeSize], outputLocal, this->tileLength * dtypeSize);
        // 释放本地缓存
        outQueueOutput.FreeTensor(outputLocal);
    }
};

// LCM设备函数（独立实现）
template <typename T>
__device__ T lcm_device(T a, T b) {
    if (a == 0 || b == 0) return 0;
    T abs_a = a > 0 ? a : -a;
    T abs_b = b > 0 ? b : -b;
    T x = abs_a, y = abs_b;
    while (y != 0) {
        T t = y;
        y = x % y;
        x = t;
    }
    // 先除后乘防止溢出（尤其int64）
    return (abs_a / x) * abs_b;
}

extern "C" __global__ __aicore__ void lcm(GM_ADDR workspace, GM_ADDR tiling) {
    GET_TILING_DATA(tiling_data, tiling);
    Lcmkernel kernel;
    // 注意：Init函数新增output_dtype参数
    kernel.Init(tiling_data.input, tiling_data.other, tiling_data.output, 
                tiling_data.totalLength, tiling_data.tileNum, tiling_data.output_dtype);
    kernel.Process();
}