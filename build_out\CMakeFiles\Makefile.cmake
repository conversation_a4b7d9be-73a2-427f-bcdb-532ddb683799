# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.16.5/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.16.5/CMakeSystem.cmake"
  "../cmake/config.cmake"
  "../cmake/func.cmake"
  "../cmake/intf.cmake"
  "../framework/CMakeLists.txt"
  "../framework/tf_plugin/CMakeLists.txt"
  "../op_host/CMakeLists.txt"
  "../op_kernel/CMakeLists.txt"
  "/usr/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake/Modules/CPack.cmake"
  "/usr/share/cmake/Modules/CPackComponent.cmake"
  "/usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux.cmake"
  "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake/Templates/CPackConfig.cmake.in"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.16.5/CMakeSystem.cmake"
  "CMakeFiles/3.16.5/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.16.5/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.5/CMakeCXXCompiler.cmake"
  "CPackConfig.cmake"
  "CPackSourceConfig.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "framework/CMakeFiles/CMakeDirectoryInformation.cmake"
  "framework/tf_plugin/CMakeFiles/CMakeDirectoryInformation.cmake"
  "op_host/CMakeFiles/CMakeDirectoryInformation.cmake"
  "op_kernel/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/modify_vendor.dir/DependInfo.cmake"
  "CMakeFiles/gen_version_info.dir/DependInfo.cmake"
  "framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/DependInfo.cmake"
  "op_host/CMakeFiles/cust_optiling.dir/DependInfo.cmake"
  "op_host/CMakeFiles/cust_op_proto.dir/DependInfo.cmake"
  "op_host/CMakeFiles/cust_opapi.dir/DependInfo.cmake"
  "op_host/CMakeFiles/optiling_compat.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend310b_gen_ops_config.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/binary.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_0.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend310b.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend310b_sigmoid_custom_copy.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend910b.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_0.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ops_info_gen_ascend310b.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_impl_gen.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ops_info_gen_ascend910b.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend910b_gen_ops_config.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/npu_supported_ops.dir/DependInfo.cmake"
  "op_kernel/CMakeFiles/ascendc_bin_ascend910b_sigmoid_custom_copy.dir/DependInfo.cmake"
  )
