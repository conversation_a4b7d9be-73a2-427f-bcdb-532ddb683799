
#include "register/tilingdata_base.h"

namespace optiling {
// LcmTilingData 用于描述 LCM 算子的分块（tiling）参数。
// 结合题目要求，需支持：
// 1. 输入 shape 可能为1~3维，且每一维不一定是32的倍数
// 2. 支持 int8/int16/int32/int64，且 int64 取值可能超int32
// 3. 支持广播机制
// 4. 需记录输入/输出 shape、数据类型、总元素数等信息
// 5. 禁止 int64 到 int32 的强制转换
// 6. 需处理非32整倍数对齐
BEGIN_TILING_DATA_DEF(LcmTilingData)
  // 核心计算参数
  TILING_DATA_FIELD_DEF(uint32_t, totalLength);     // 广播后输出张量总元素数（原始未对齐）
  TILING_DATA_FIELD_DEF(uint32_t, blockLength);      // 每个核分配的元素个数（对齐后）
  TILING_DATA_FIELD_DEF(uint32_t, tileNum);          // 每个核的分块数量
  TILING_DATA_FIELD_DEF(uint32_t, output_dtype);     // 输出数据类型（0:int8,1:int16,2:int32,3:int64）
  
  // 非对齐处理参数[2,10](@ref)
  TILING_DATA_FIELD_DEF(uint32_t, alignNum);         // 对齐单位（元素个数）
  TILING_DATA_FIELD_DEF(uint32_t, validBlockLength); // 当前核有效数据长度（防溢出）
  
  // 广播支持参数
  TILING_DATA_FIELD_DEF(uint32_t, shape0);           // 广播后第一维长度
  TILING_DATA_FIELD_DEF(uint32_t, shape1);           // 广播后第二维长度
  TILING_DATA_FIELD_DEF(uint32_t, shape2);           // 广播后第三维长度
END_TILING_DATA_DEF;

// 注册 Lcm 算子的 tiling 数据结构，便于框架自动识别和调用
REGISTER_TILING_DATA_CLASS(Lcm, LcmTilingData)
}
