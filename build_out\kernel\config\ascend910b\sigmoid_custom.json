{"binList": [{"implMode": "high_performance", "int64Mode": false, "simplifiedKeyMode": 0, "simplifiedKey": ["SigmoidCustom/d=0,p=1/1,2/1,2", "SigmoidCustom/d=0,p=0/1,2/1,2", "SigmoidCustom/d=1,p=1/1,2/1,2", "SigmoidCustom/d=1,p=0/1,2/1,2"], "staticKey": "a84e7dc2efda02122a97c46caef20dee738722bcd3e897f326f356bc1226a677,6ea53f6da880a8179848e8d27d9656470fcc75d7d663e87a5ddd49a942d586d5", "inputs": [{"name": "x", "index": 0, "dtype": "float16", "format": "ND", "paramType": "required", "shape": [-2], "format_match_mode": "FormatAgnostic"}], "outputs": [{"name": "y", "index": 0, "dtype": "float16", "format": "ND", "paramType": "required", "shape": [-2], "format_match_mode": "FormatAgnostic"}], "opMode": "dynamic", "optionalInputMode": "gen_placeholder", "deterministic": "ignore", "optionalOutputMode": "gen_placeholder", "binInfo": {"jsonFilePath": "ascend910b/sigmoid_custom/SigmoidCustom_a3c9eb1f1b227778957282b95ed93786.json"}}]}