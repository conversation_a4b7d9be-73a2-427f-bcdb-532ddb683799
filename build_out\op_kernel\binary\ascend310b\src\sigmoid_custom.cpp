#include "kernel_operator.h"
using namespace AscendC;
constexpr int32_t BUFFER_NUM = 2;
class KernelSigmoid {
public:
    __aicore__ inline KernelSigmoid() {}
    __aicore__ inline void Init(GM_ADDR x, GM_ADDR y, uint32_t totalLength, uint32_t tileNum)
    {
        //考生补充初始化代码
        ASSERT(GetBlockNum() != 0 && "block dim can not be zero!");
        this->blockLength = totalLength / GetBlockNum();
        this->tileNum = tileNum;
        ASSERT(tileNum != 0 && "tile num can not be zero!");
        this->tileLength = this->blockLength / tileNum / BUFFER_NUM;
        xGm.SetGlobalBuffer((__gm__ DTYPE_X *)x + this->blockLength * GetBlockIdx(), 
        this->blockLength);
        yGm.SetGlobalBuffer((__gm__ DTYPE_Y *)y + this->blockLength * GetBlockIdx(), 
        this->blockLength);
        pipe.InitBuffer(inQueueX, BUFFER_NUM, this->tileLength * sizeof(half));
        pipe.InitBuffer(outQueueY, BUFFER_NUM, this->tileLength * sizeof(half));
        pipe.InitBuffer(tmpBuffer1, this->tileLength * sizeof(half));
        pipe.InitBuffer(tmpBuffer2, this->tileLength * sizeof(half));
        pipe.InitBuffer(tmpBuffer3, this->tileLength * sizeof(half));
        pipe.InitBuffer(tmpBuffer4, this->tileLength * sizeof(half));
    }
    __aicore__ inline void Process()
    {
        // 补充对“loopCount”的定义，注意对Tiling的处理
        int32_t loopCount = this->blockLength / this->tileLength;
        for (int32_t i = 0; i < loopCount; i++) {
            CopyIn(i);
            Compute(i);
            CopyOut(i);
        }
    }
 
private:
    __aicore__ inline void CopyIn(int32_t progress)
    {
        //考生补充算子代码
        LocalTensor<DTYPE_X> xLocal = inQueueX.AllocTensor<DTYPE_X>();
        DataCopy(xLocal, xGm[progress * this->tileLength ], this->tileLength);
        inQueueX.EnQue(xLocal);
    }
    __aicore__ inline void Compute(int32_t progress)
    {
        //考生补充算子计算代码·核心部分
        // 1.从输入队列中取出当前块的数据
        LocalTensor<half> xLocal = inQueueX.DeQue<half>();
 
        // 2.从输出队列分配空间用于存储结果
        LocalTensor<half> yLocal = outQueueY.AllocTensor<half>();
 
        // 3.获取临时缓冲区用于中间计算
        LocalTensor<half> tmpTensor1 = tmpBuffer1.Get<half>();
        LocalTensor<half> tmpTensor2 = tmpBuffer2.Get<half>();
        LocalTensor<half> tmpTensor3 = tmpBuffer3.Get<half>();
        //LocalTensor<half> tmpTensor4 = tmpBuffer4.Get<half>();
 
        // 获取临时缓冲区用于存储全1向量
        LocalTensor<half> oneVec = tmpBuffer4.Get<half>();
    
        // 初始化全1向量
        DTYPE_X zero = 0.0;
        DTYPE_X one = 1.0;
        Muls(oneVec, oneVec, zero, this->tileLength);
        Adds(oneVec, oneVec, one, this->tileLength);
 
        // 定义sigmoid计算使用的常量
        DTYPE_X inputVal1 = -1.0;
        DTYPE_X inputVal2 = 1.0;
        // 计算步骤1: x = -x
        Muls(tmpTensor1, xLocal, inputVal1, this->tileLength);
        // 计算步骤2: exp(-x)
        Exp(tmpTensor2, tmpTensor1, this->tileLength);
        // 计算步骤3: 1 + exp(-x)
        Adds(tmpTensor3, tmpTensor2, inputVal2, this->tileLength);
        // 计算步骤4: 1 / (1 + exp(-x)) 得到最终的sigmoid结果
        Div(yLocal, oneVec, tmpTensor3, this->tileLength);
 
        // 将结果放入输出队列
        outQueueY.EnQue(yLocal);
 
        // 释放输入数据占用的空间
        inQueueX.FreeTensor(xLocal);
    }
    __aicore__ inline void CopyOut(int32_t progress)
    {
        // 考生补充算子代码
        // 从队列取出计算结果
        LocalTensor<half> yLocal = outQueueY.DeQue<half>();
        
        // 复制到全局内存
        DataCopy(yGm[progress * this->tileLength], 
                yLocal, 
                this->tileLength);
        
        // 释放本地缓存
        outQueueY.FreeTensor(yLocal);
    }
 
private:
    TPipe pipe;
    //create queue for input, in this case depth is equal to buffer num
    TQue<QuePosition::VECIN, BUFFER_NUM> inQueueX;
    //create queue for output, in this case depth is equal to buffer num
    TQue<QuePosition::VECOUT, BUFFER_NUM> outQueueY;
    GlobalTensor<half> xGm;
    GlobalTensor<half> yGm;
 
    //考生补充自定义成员变量
    TBuf<QuePosition::VECCALC> tmpBuffer1, tmpBuffer2, tmpBuffer3, tmpBuffer4;
    uint32_t blockLength;
    uint32_t tileNum;
    uint32_t tileLength;
};

extern "C" __global__ __aicore__ void sigmoid_custom(GM_ADDR x, GM_ADDR y, GM_ADDR workspace, GM_ADDR tiling) {
    // 获取Tiling参数
    GET_TILING_DATA(tiling_data, tiling);
    
    // 创建算子实例
    KernelSigmoid op;
    
    // 初始化并执行
    op.Init(x, y, tiling_data.totalLength, tiling_data.tileNum);
    op.Process();
}