# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/base_type.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/compute_node_info.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/context_extend.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/expand_dims_type.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_datatype_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_range_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_run_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/op_execute_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/range.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor_data.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_data.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_parse_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_error_codes.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_types.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/ge/ge_allocator.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/compiler_def.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/gnode.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_factory.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_reg.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_impl_registry.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/securec.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/securectype.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/utils/extern_math_util.h
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: autogen/op_proto.cc
op_host/CMakeFiles/cust_op_proto.dir/__/autogen/op_proto.cc.o: autogen/op_proto.h

op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/base_type.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/compute_node_info.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/context_extend.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/expand_dims_type.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_datatype_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_range_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_run_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/op_execute_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/range.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor_data.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_data.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_parse_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_error_codes.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_types.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_error_codes.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/ge/ge_allocator.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/compiler_def.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/gnode.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_factory.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_reg.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_factory.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_registry.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_impl_registry.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/register/tilingdata_base.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/securec.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/securectype.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: /home/<USER>/Ascend/ascend-toolkit/latest/include/utils/extern_math_util.h
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: ../op_host/sigmoid_custom.cpp
op_host/CMakeFiles/cust_op_proto.dir/sigmoid_custom.cpp.o: ../op_host/sigmoid_custom_tiling.h

