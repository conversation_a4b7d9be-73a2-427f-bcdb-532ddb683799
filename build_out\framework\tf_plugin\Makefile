# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/framework/tf_plugin/CMakeFiles/progress.marks
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f CMakeFiles/Makefile2 framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule
.PHONY : framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule

# Convenience name for target.
cust_tf_parsers: framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/rule

.PHONY : cust_tf_parsers

# fast build rule for target.
cust_tf_parsers/fast:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build
.PHONY : cust_tf_parsers/fast

tensorflow_sigmoid_custom_plugin.o: tensorflow_sigmoid_custom_plugin.cc.o

.PHONY : tensorflow_sigmoid_custom_plugin.o

# target to build an object file
tensorflow_sigmoid_custom_plugin.cc.o:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/tensorflow_sigmoid_custom_plugin.cc.o
.PHONY : tensorflow_sigmoid_custom_plugin.cc.o

tensorflow_sigmoid_custom_plugin.i: tensorflow_sigmoid_custom_plugin.cc.i

.PHONY : tensorflow_sigmoid_custom_plugin.i

# target to preprocess a source file
tensorflow_sigmoid_custom_plugin.cc.i:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/tensorflow_sigmoid_custom_plugin.cc.i
.PHONY : tensorflow_sigmoid_custom_plugin.cc.i

tensorflow_sigmoid_custom_plugin.s: tensorflow_sigmoid_custom_plugin.cc.s

.PHONY : tensorflow_sigmoid_custom_plugin.s

# target to generate assembly for a file
tensorflow_sigmoid_custom_plugin.cc.s:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(MAKE) -f framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/build.make framework/tf_plugin/CMakeFiles/cust_tf_parsers.dir/tensorflow_sigmoid_custom_plugin.cc.s
.PHONY : tensorflow_sigmoid_custom_plugin.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... package"
	@echo "... package_source"
	@echo "... cust_tf_parsers"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... list_install_components"
	@echo "... tensorflow_sigmoid_custom_plugin.o"
	@echo "... tensorflow_sigmoid_custom_plugin.i"
	@echo "... tensorflow_sigmoid_custom_plugin.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/work/SigmoidCustom/SigmoidCustom/build_out && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

