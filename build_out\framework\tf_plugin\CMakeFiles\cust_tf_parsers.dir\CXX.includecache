#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
string
-
memory
-
functional
-
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
cstdint
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
memory
-
string
-
vector
-
set
-
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
resource_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h
ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
functional
-
map
-
memory
-
string
-
vector
-
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./inference_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
./tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
atomic
-
memory
-
string
-
vector
-
utility
-
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
atomic
-
memory
-
vector
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register.h
functional
-
initializer_list
-
map
-
memory
-
set
-
string
-
utility
-
unordered_map
-
vector
-
graph/operator.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/operator.h
register/register_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/register_error_codes.h
register/register_fmk_types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/register_fmk_types.h
register/register_types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/register_types.h
graph/ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/ascend_string.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register_fmk_types.h
string
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register_types.h

/home/<USER>/work/SigmoidCustom/SigmoidCustom/framework/tf_plugin/tensorflow_sigmoid_custom_plugin.cc
register/register.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/framework/tf_plugin/register/register.h

