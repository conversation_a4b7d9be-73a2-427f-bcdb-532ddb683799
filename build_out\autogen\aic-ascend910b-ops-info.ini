[SigmoidCustom]
input0.name=x
input0.dtype=float16
input0.format=ND
input0.unknownshape_format=ND
input0.shape=all
input0.paramType=required
output0.name=y
output0.dtype=float16
output0.format=ND
output0.unknownshape_format=ND
output0.shape=all
output0.paramType=required
dynamicCompileStatic.flag=true
dynamicFormat.flag=true
dynamicRankSupport.flag=true
dynamicShapeSupport.flag=true
needCheckSupport.flag=false
precision_reduce.flag=true
opFile.value=sigmoid_custom
opInterface.value=sigmoid_custom
