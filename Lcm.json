{"op": "Lcm", "language": "cpp", "input_desc": [{"name": "input", "dtype": ["int8", "int16", "int32", "int64"], "shape": ["[N]", "[N,N2]", "[N,N2,N3]"], "range": {"N": [1, 200], "N2": [1, 4000], "N3": [1, 3000]}, "broadcastable": false, "special_notes": ["维度可能非32整倍数", "int64取值范围可能超出int32"]}, {"name": "other", "dtype": ["int8", "int16", "int32", "int64"], "shape": ["[N]", "[N,N2]", "[N,N2,N3]"], "range": {"N": [1, 200], "N2": [1, 4000], "N3": [1, 3000]}, "broadcastable": true, "special_notes": ["支持广播机制", "不支持维度自动扩展"]}], "output_desc": {"name": "out", "dtype": ["int8", "int16", "int32", "int64"], "shape": ["[N]", "[N,N2]", "[N,N2,N3]"], "special_notes": ["输出形状与输入广播后一致", "保持原始数据类型"]}, "constraints": ["输入张量维度必须兼容广播规则", "严格禁止int64到int32的强制转换", "需处理非32整倍数对齐场景"]}