{"binFileName": "SigmoidCustom_a3c9eb1f1b227778957282b95ed93786", "binFileSuffix": ".o", "blockDim": -1, "coreType": "VectorCore", "core_type": "AIV", "intercoreSync": 0, "kernelName": "SigmoidCustom_a3c9eb1f1b227778957282b95ed93786", "magic": "RT_DEV_BINARY_MAGIC_ELF_AIVEC", "memoryStamping": [], "opParaSize": 16, "parameters": [null, null, null], "sha256": "77f9376330f53d4775bdc0f8dfbb4255f93ec186bc39238a783277c7bb9c6d37", "workspace": {"num": 1, "size": [-1], "type": [0]}, "kernelList": [{"kernelName": "SigmoidCustom_a3c9eb1f1b227778957282b95ed93786_0"}], "optionalInputMode": "gen_placeholder", "optionalOutputMode": "gen_placeholder", "compileInfo": {}, "supportInfo": {"implMode": "high_performance", "int64Mode": false, "simplifiedKeyMode": 0, "simplifiedKey": ["SigmoidCustom/d=0,p=1/1,2/1,2", "SigmoidCustom/d=0,p=0/1,2/1,2", "SigmoidCustom/d=1,p=1/1,2/1,2", "SigmoidCustom/d=1,p=0/1,2/1,2"], "optionalInputMode": "gen_placeholder", "optionalOutputMode": "gen_placeholder", "staticKey": "a84e7dc2efda02122a97c46caef20dee738722bcd3e897f326f356bc1226a677,6ea53f6da880a8179848e8d27d9656470fcc75d7d663e87a5ddd49a942d586d5", "inputs": [{"name": "x", "index": 0, "dtype": "float16", "format": "ND", "paramType": "required", "shape": [-2], "format_match_mode": "FormatAgnostic"}], "outputs": [{"name": "y", "index": 0, "dtype": "float16", "format": "ND", "paramType": "required", "shape": [-2], "format_match_mode": "FormatAgnostic"}], "opMode": "dynamic", "deterministic": "ignore"}, "filePath": "ascend910b/bin/sigmoid_custom/SigmoidCustom_a3c9eb1f1b227778957282b95ed93786.json"}