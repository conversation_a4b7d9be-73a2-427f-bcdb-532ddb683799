#include "lcm_tiling.h"
#include "register/op_def_registry.h"

const uint32_t BLOCK_DIM = 8; // 硬件执行单元数
const uint32_t TILE_NUM = 8;  // 数据分块数

namespace optiling {
static ge::graphStatus TilingFunc(gert::TilingContext* context)
{
  LcmTilingData tiling;
  
  // 1. 获取广播后的输出形状及元素总数
  const gert::Shape* output_shape = context->GetOutputShape(0);
  uint32_t totalLength = output_shape->GetShapeSize();
  
  // 2. 获取数据类型（取第一个输入的数据类型）
  ge::DataType data_type = context->GetInputTensorDesc(0)->GetDataType();
  uint32_t output_dtype = 0;
  switch(data_type) {
    case ge::DT_INT8: output_dtype = 0; break;
    case ge::DT_INT16: output_dtype = 1; break;
    case ge::DT_INT32: output_dtype = 2; break;
    case ge::DT_INT64: output_dtype = 3; break;
  }
  
  // 3. 计算对齐单位（根据数据类型）
  uint32_t dtype_size = (output_dtype == 0) ? 1 : 
                       (output_dtype == 1) ? 2 :
                       (output_dtype == 2) ? 4 : 8;
  uint32_t alignNum = 32 / dtype_size; // 32字节对齐

  // 4. 计算每个核处理的数据量（考虑对齐）
  uint32_t blockLength = (totalLength + BLOCK_DIM - 1) / BLOCK_DIM;
  blockLength = (blockLength + alignNum - 1) / alignNum * alignNum;
  
  // 5. 获取广播后形状的维度信息（最多三维）
  uint32_t dim_num = output_shape->GetDimNum();
  uint32_t shape0 = (dim_num > 0) ? output_shape->GetDim(dim_num-1) : 1;
  uint32_t shape1 = (dim_num > 1) ? output_shape->GetDim(dim_num-2) : 1;
  uint32_t shape2 = (dim_num > 2) ? output_shape->GetDim(dim_num-3) : 1;

  // 6. 设置Tiling参数
  context->SetBlockDim(BLOCK_DIM); 
  tiling.set_totalLength(totalLength);
  tiling.set_blockLength(blockLength);
  tiling.set_tileNum(TILE_NUM);
  tiling.set_output_dtype(output_dtype);
  tiling.set_alignNum(alignNum);
  tiling.set_shape0(shape0);
  tiling.set_shape1(shape1);
  tiling.set_shape2(shape2);
  
  // 7. 序列化Tiling参数
  tiling.SaveToBuffer(context->GetRawTilingData()->GetData(), 
                      context->GetRawTilingData()->GetCapacity());
  context->GetRawTilingData()->SetDataSize(tiling.GetDataSize());
  
  // 设置Workspace内存（本例中为0）
  size_t *currentWorkspace = context->GetWorkspaceSizes(1);
  currentWorkspace[0] = 0;

  return ge::GRAPH_SUCCESS;
}
}

namespace ge {
static ge::graphStatus InferShape(gert::InferShapeContext* context)
{
    // 1. 获取输入形状
    const gert::Shape* input_shape = context->GetInputShape(0);
    const gert::Shape* other_shape = context->GetInputShape(1);
    gert::Shape* output_shape = context->GetOutputShape(0);
    
    // 2. 计算广播后的维度数
    uint32_t input_dims = input_shape->GetDimNum();
    uint32_t other_dims = other_shape->GetDimNum();
    uint32_t max_dims = std::max(input_dims, other_dims);
    
    // 3. 逐维度计算广播后形状
    std::vector<int64_t> output_dims(max_dims);
    for (uint32_t i = 0; i < max_dims; i++) {
        int64_t dim1 = (i < input_dims) ? 
            input_shape->GetDim(input_dims - 1 - i) : 1;
        int64_t dim2 = (i < other_dims) ? 
            other_shape->GetDim(other_dims - 1 - i) : 1;
        
        // 检查维度兼容性（题目要求）
        if (dim1 != dim2 && dim1 != 1 && dim2 != 1) {
            return ge::GRAPH_FAILED; // 维度不兼容
        }
        output_dims[max_dims - 1 - i] = std::max(dim1, dim2);
    }
    
    // 4. 设置输出形状
    output_shape->SetDimNum(max_dims);
    for (uint32_t i = 0; i < max_dims; i++) {
        output_shape->SetDim(i, output_dims[i]);
    }
    
    return GRAPH_SUCCESS;
}
}

namespace ops {
class Lcm : public OpDef {
public:
    explicit Lcm(const char* name) : OpDef(name)
    {
        // 输入input：不支持广播
        this->Input("input")
            .ParamType(REQUIRED)
            .DataType({ge::DT_INT8, ge::DT_INT16, ge::DT_INT32, ge::DT_INT64})
            .Format({ge::FORMAT_ND})
            .UnknownShapeFormat({ge::FORMAT_ND});
        
        // 输入other：支持广播
        this->Input("other")
            .ParamType(REQUIRED)
            .DataType({ge::DT_INT8, ge::DT_INT16, ge::DT_INT32, ge::DT_INT64})
            .Format({ge::FORMAT_ND})
            .UnknownShapeFormat({ge::FORMAT_ND})
            .Broadcastable(); // 关键：启用广播支持
        
        // 输出：形状与广播后一致
        this->Output("output")
            .ParamType(REQUIRED)
            .DataType({ge::DT_INT8, ge::DT_INT16, ge::DT_INT32, ge::DT_INT64})
            .Format({ge::FORMAT_ND})
            .UnknownShapeFormat({ge::FORMAT_ND});
        
        // 设置形状推断函数
        this->SetInferShape(ge::InferShape);

        // 设置分块函数和硬件配置
        this->AICore()
            .SetTiling(optiling::TilingFunc);
        this->AICore().AddConfig("ascend910");
    }
};

OP_ADD(Lcm);
}