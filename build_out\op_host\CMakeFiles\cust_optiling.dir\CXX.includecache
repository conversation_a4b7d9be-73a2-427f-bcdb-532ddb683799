#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/base_type.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/compute_node_info.h
type_traits
-
cstdint
-
cstddef
-
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/types.h
storage_format.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/context_extend.h
type_traits
-
memory
-
compute_node_info.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/compute_node_info.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
cstddef
-
cstdint
-
memory
-
type_traits
-
securec.h
-
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/ge_error_codes.h
utils/extern_math_util.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/utils/extern_math_util.h
external/ge_common/ge_api_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/external/ge_common/ge_api_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/expand_dims_type.h
cstdint
-
cstddef
-
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/ge_error_codes.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/types.h
shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
type_traits
-
kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_context.h
context_extend.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/context_extend.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_datatype_context.h
type_traits
-
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_context.h
type_traits
-
shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/infer_shape_range_context.h
type_traits
-
range.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/range.h
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_context.h
type_traits
-
kernel_run_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_run_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/kernel_run_context.h
stdlib.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/op_execute_context.h
type_traits
-
exe_graph/runtime/shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/shape.h
exe_graph/runtime/tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/tensor.h
exe_graph/runtime/runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/runtime_attrs.h
exe_graph/runtime/extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/extended_kernel_context.h
ge/ge_allocator.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/ge/ge_allocator.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/range.h
array
-
iostream
-
utils/extern_math_util.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/utils/extern_math_util.h
shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/runtime_attrs.h
cstdint
-
type_traits
-
cstddef
-
continuous_vector.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h
array
-
vector
-
iostream
-
cstring
-
type_traits
-
limits
-
utils/extern_math_util.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/utils/extern_math_util.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
memory
-
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/types.h
expand_dims_type.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/expand_dims_type.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
type_traits
-
shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/shape.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
cstring
-
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/ge_error_codes.h
storage_shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
storage_format.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_format.h
tensor_data.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor_data.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor_data.h
cstddef
-
cstdint
-
cstring
-
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/ge_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_context.h
storage_shape.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/storage_shape.h
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tensor.h
continuous_vector.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/continuous_vector.h
extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/extended_kernel_context.h
tiling_data.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_data.h
external/ge_common/ge_api_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/external/ge_common/ge_api_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_data.h
cstdint
-
cstddef
-
cstring
-
type_traits
-
securec.h
-
exe_graph/runtime/continuous_vector.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/continuous_vector.h
exe_graph/runtime/runtime_attrs.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/runtime_attrs.h
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/ge_error_codes.h
utils/extern_math_util.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/utils/extern_math_util.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/tiling_parse_context.h
exe_graph/runtime/extended_kernel_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/exe_graph/runtime/extended_kernel_context.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/exe_graph/runtime/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_error_codes.h
map
-
string
-
ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_error_codes.h
ge_api_types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_types.h
graph/ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/graph/ascend_string.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_api_types.h
cstdint
-
string
-
vector
-
set
-
functional
-
memory
-
graph/tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/graph/tensor.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/external/ge_common/ge_error_codes.h
stddef.h
-
stdint.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/ge/ge_allocator.h
cstdlib
-
memory
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
string
-
memory
-
functional
-
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/compiler_def.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
cstdint
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/gnode.h
vector
-
cstdint
-
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
./tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
./ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph.h
memory
-
string
-
utility
-
vector
-
./operator.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
./gnode.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/gnode.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
memory
-
string
-
vector
-
set
-
tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
resource_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h
ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
functional
-
map
-
memory
-
string
-
vector
-
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./inference_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/inference_context.h
./tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_factory.h
map
-
memory
-
string
-
vector
-
./operator.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator.h
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/operator_reg.h
functional
-
memory
-
string
-
vector
-
graph/operator.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/operator.h
graph/operator_factory.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/operator_factory.h
graph/tensor.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/tensor.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/types.h
graph/graph.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/graph/graph.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/resource_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/tensor.h
atomic
-
memory
-
string
-
vector
-
utility
-
./ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ge_error_codes.h
./types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/ascend_string.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/graph/types.h
atomic
-
memory
-
vector
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def.h
iostream
-
vector
-
memory
-
register/op_impl_registry.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/op_impl_registry.h
graph/operator_reg.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/operator_reg.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_factory.h
register/op_def.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/op_def.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_def_registry.h
register/op_def.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/op_def.h
register/op_def_factory.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/register/op_def_factory.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/op_impl_registry.h
initializer_list
-
string
-
map
-
unordered_set
-
graph/ge_error_codes.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/ge_error_codes.h
graph/types.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/types.h
graph/compiler_def.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/compiler_def.h
exe_graph/runtime/base_type.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/base_type.h
exe_graph/runtime/infer_shape_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/infer_shape_context.h
exe_graph/runtime/infer_shape_range_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/infer_shape_range_context.h
exe_graph/runtime/infer_datatype_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/infer_datatype_context.h
exe_graph/runtime/tiling_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/tiling_context.h
exe_graph/runtime/tiling_parse_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/tiling_parse_context.h
exe_graph/runtime/op_execute_context.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/exe_graph/runtime/op_execute_context.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/register/tilingdata_base.h
vector
-
map
-
memory
-
cstring
-
securec.h
-
graph/ascend_string.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/register/graph/ascend_string.h

/home/<USER>/Ascend/ascend-toolkit/latest/include/securec.h
securectype.h
/home/<USER>/Ascend/ascend-toolkit/latest/include/securectype.h
stdarg.h
-
linux/errno.h
-
errno.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/securectype.h
linux/kernel.h
-
linux/module.h
-
stdio.h
-
string.h
-
stdlib.h
-
stddef.h
-

/home/<USER>/Ascend/ascend-toolkit/latest/include/utils/extern_math_util.h
iostream
-
cstdint
-
limits
-

/home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/sigmoid_custom.cpp
sigmoid_custom_tiling.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/sigmoid_custom_tiling.h
register/op_def_registry.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/register/op_def_registry.h

/home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/sigmoid_custom_tiling.h
register/tilingdata_base.h
/home/<USER>/work/SigmoidCustom/SigmoidCustom/op_host/register/tilingdata_base.h

